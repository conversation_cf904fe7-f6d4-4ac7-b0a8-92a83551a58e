#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
客户手机号查询服务器
"""

# 导入必要的库
import os
import json
import time
import requests
import traceback
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, send_from_directory, session, redirect
from flask_session import Session
from flask_cors import CORS
from functools import wraps
# 导入自定义模块
from database import UserAuth, test_database_connection
from query_logger import QueryLogger
from feishu_auth import FeishuAuth

app = Flask(__name__)
CORS(app, supports_credentials=True)

# Session配置 - 修复Flask-Session兼容性问题
app.config['SECRET_KEY'] = 'zm_customer_query_system_2025'
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_FILE_DIR'] = './flask_session'
app.config['SESSION_PERMANENT'] = True
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)
app.config['SESSION_USE_SIGNER'] = True  # 启用签名验证，提高安全性
app.config['SESSION_KEY_PREFIX'] = 'zm_customer:'
app.config['SESSION_COOKIE_SECURE'] = False  # HTTP部署环境设为False，HTTPS时改为True
app.config['SESSION_COOKIE_HTTPONLY'] = True   # 提高安全性，防止XSS攻击
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'   # CSRF保护，允许安全的跨站请求
app.config['SESSION_COOKIE_NAME'] = 'zm_session'
app.config['SESSION_FILE_THRESHOLD'] = 100  # 限制session文件数量
app.config['SESSION_COOKIE_DOMAIN'] = None  # 不限制域名
app.config['SESSION_COOKIE_PATH'] = '/'  # 明确设置路径
app.config['SESSION_SERIALIZATION_FORMAT'] = 'json'  # 使用JSON序列化，避免pickle问题

# 基础配置
BASE_DOMAIN = os.environ.get('BASE_DOMAIN', 'http://localhost:5000')

# API配置
API_CONFIG = {
    'auth_url': 'https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login',
    'customer_url': 'https://em02-ym-openapi-admin.linkedcare.cn/api/v1/crm/customer',
    'credentials': {
        'appId': '40e38a58-5c66-4120-997a-befd595e0e48',
        'code': 'sh-zmyh',
        'secret': 'MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5'
    }
}

# Token缓存
token_cache = {
    'token': None,
    'expire_time': 0,
    'buffer_seconds': 300  # 提前5分钟刷新token
}

# 用户认证实例
user_auth = UserAuth()

# 初始化日志记录器
query_logger = QueryLogger()

# 初始化飞书认证
feishu_auth = FeishuAuth()

# 基础配置

# 初始化Session
Session(app)

def get_client_info():
    """获取客户端信息"""
    ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    user_agent = request.headers.get('User-Agent', 'unknown')
    return ip_address, user_agent

def get_user_info_from_session():
    """从session中获取用户信息"""
    return {
        'user_id': session.get('user_id'),
        'user_email': session.get('user_email', ''),
        'user_name': session.get('username', '')
    }

# 工具函数
def get_client_info():
    """获取客户端信息"""
    ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    user_agent = request.headers.get('User-Agent', 'unknown')
    return ip_address, user_agent

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({
                'success': False,
                'message': '请先登录',
                'error_code': 'NOT_LOGGED_IN'
            }), 401
        return f(*args, **kwargs)
    return decorated_function

def get_cached_token():
    """获取缓存的Token，如果过期则自动刷新"""
    current_time = time.time() * 1000  # 转换为毫秒

    # 检查缓存的token是否有效
    if (token_cache['token'] and
        token_cache['expire_time'] > current_time + token_cache['buffer_seconds'] * 1000):
        print(f"使用缓存Token: {token_cache['token'][:20]}...")
        return token_cache['token']

    # Token过期或不存在，获取新token
    return refresh_token()

def refresh_token():
    """刷新Token"""
    try:
        print(f"=== 获取新Token ===")
        print(f"请求URL: {API_CONFIG['auth_url']}")
        print(f"请求数据: {API_CONFIG['credentials']}")

        response = requests.post(
            API_CONFIG['auth_url'],
            json=API_CONFIG['credentials'],
            headers={'Content-Type': 'application/json'},
            timeout=10
        )

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code in [200, 201]:
            result = response.json()
            token = result.get("accessToken") or result.get("token")
            expire_at = result.get("expireAt", 0)

            if token:
                # 缓存token和过期时间
                token_cache['token'] = token
                token_cache['expire_time'] = expire_at

                # 计算token有效期
                current_time = time.time() * 1000
                valid_duration = (expire_at - current_time) / 1000 / 60  # 转换为分钟

                print(f"Token获取成功: {token[:20]}...")
                print(f"Token过期时间: {expire_at}")
                print(f"Token有效期: {valid_duration:.1f}分钟")

                return token
            else:
                print("响应中没有找到token字段")
                return None
        else:
            print(f"Token获取失败，状态码: {response.status_code}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"Token获取异常: {e}")
        return None

def query_customer_info(keyword, token):
    """查询客户信息 - 只返回会员号完全匹配的结果"""
    try:
        print(f"=== 查询客户信息 ===")
        print(f"关键词: {keyword}")
        print(f"Token: {token[:20]}...")

        params = {'keyword': keyword}
        headers = {
            'x-access-token': token,
            'Content-Type': 'application/json'
        }

        print(f"请求URL: {API_CONFIG['customer_url']}")
        print(f"请求参数: {params}")
        print(f"请求头: {headers}")

        response = requests.get(
            API_CONFIG['customer_url'],
            params=params,
            headers=headers,
            timeout=10
        )

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            print(f"查询成功，返回数据: {result}")

            # 提取content数组，这是实际的客户数据
            raw_data = result.get('content', result) if 'content' in result else result

            if not raw_data or not isinstance(raw_data, list):
                print(f"❌ 没有找到客户数据或数据格式错误")
                return None

            # 过滤出会员号完全匹配的客户
            matched_customers = []
            input_keyword = str(keyword).strip()

            for customer in raw_data:
                # 检查多个可能的会员号字段
                customer_member_codes = []

                # 常见的会员号字段名
                possible_fields = ['number', 'memberCode', 'code', 'member_code', 'customerCode', 'id']

                for field in possible_fields:
                    if field in customer and customer[field]:
                        customer_member_codes.append(str(customer[field]).strip())

                # 检查是否有完全匹配的会员号
                for member_code in customer_member_codes:
                    if member_code == input_keyword:
                        print(f"✅ 找到匹配的会员号: {member_code}")
                        matched_customers.append(customer)
                        break  # 找到匹配就跳出内层循环

            if matched_customers:
                print(f"✅ 会员号完全匹配验证通过，返回 {len(matched_customers)} 条匹配记录")
                return matched_customers
            else:
                print(f"❌ 没有找到会员号完全匹配的客户记录")
                return None
        else:
            print(f"查询失败，状态码: {response.status_code}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"查询异常: {e}")
        return None

@app.route('/')
def index():
    """主页面"""
    return send_from_directory('.', 'index.html')



@app.route('/background-styles.css')
def background_styles():
    """背景样式文件"""
    return send_from_directory('.', 'background-styles.css')

@app.route('/background-animations.js')
def background_animations():
    """背景动画脚本文件"""
    return send_from_directory('.', 'background-animations.js')

@app.route('/logs')
@login_required
def query_logs_page():
    """查询日志管理页面 - 需要登录"""
    return send_from_directory('.', 'query_logs.html')



# 路由定义

@app.route('/api/feishu/auth', methods=['GET'])
def feishu_auth_url():
    """获取飞书OAuth授权URL"""
    try:
        # 检测是否在飞书应用内环境
        user_agent = request.headers.get('User-Agent', '').lower()
        is_feishu_app = ('lark' in user_agent or 'feishu' in user_agent or 
                        'bytedance' in user_agent or 
                        request.args.get('from_feishu_app') == 'true')
        
        # 根据环境生成不同的redirect_uri，使用配置文件中的地址
        from feishu_config import FEISHU_CONFIG
        base_redirect_uri = FEISHU_CONFIG['redirect_uri']
        if is_feishu_app:
            redirect_uri = f"{base_redirect_uri}?from_feishu_app=true"
        else:
            redirect_uri = base_redirect_uri
        
        print(f"🔍 检测环境 - User-Agent: {user_agent}")
        print(f"🔍 是否飞书应用内: {is_feishu_app}")
        print(f"🔍 使用redirect_uri: {redirect_uri}")
        
        oauth_url, state = feishu_auth.generate_oauth_url(redirect_uri)
        
        # 生产环境需要存储state进行验证
        session['feishu_oauth_state'] = state
        session.permanent = True
        print(f"🔍 OAuth授权开始: 生成state={state}, 已存储到session")
        
        return jsonify({
            'success': True,
            'auth_url': oauth_url,
            'message': '请在新窗口中完成飞书授权'
        })
    except Exception as e:
        print(f"❌ 生成飞书授权URL失败: {e}")
        return jsonify({'success': False, 'message': f'授权失败: {str(e)}'}), 500

@app.route('/api/feishu/callback', methods=['GET'])
def feishu_callback():
    """飞书OAuth回调处理"""
    try:
        # 获取回调参数
        code = request.args.get('code')
        state = request.args.get('state')
        error = request.args.get('error')
        
        # 检测是否在飞书应用内环境
        user_agent = request.headers.get('User-Agent', '').lower()
        is_feishu_app = ('lark' in user_agent or 'feishu' in user_agent or 
                        'bytedance' in user_agent or 
                        request.args.get('from_feishu_app') == 'true')
        
        print(f"🔍 User-Agent: {user_agent}")
        print(f"🔍 是否飞书应用内环境: {is_feishu_app}")
        
        # 检查是否有错误
        if error:
            print(f"❌ 飞书授权错误: {error}")
            if is_feishu_app:
                # 飞书应用内环境，直接重定向到首页并显示错误
                return redirect(f'/?error=feishu_auth&message={error}')
            else:
                # 浏览器环境，使用postMessage
                return f"""
                <script>
                    window.opener.postMessage({{
                        type: 'feishu_auth_error',
                        error: '{error}'
                    }}, '*');
                    window.close();
                </script>
                """
        
        # 验证state参数，防止CSRF攻击
        stored_state = session.get('feishu_oauth_state')
        print(f"🔍 State验证 - 收到state={state}, 存储state={stored_state}")
        
        if not stored_state or stored_state != state:
            print(f"❌ State验证失败: 存储={stored_state}, 收到={state}")
            if is_feishu_app:
                return redirect('/?error=feishu_auth&message=State验证失败，请重新登录')
            else:
                return f"""
                <script>
                    window.opener.postMessage({{
                        type: 'feishu_auth_error',
                        error: 'State验证失败，请重新登录'
                    }}, '*');
                    window.close();
                </script>
                """
        
        # 清理已使用的state
        session.pop('feishu_oauth_state', None)
        print("✅ State验证通过")
        
        # 验证code参数
        if not code:
            print("❌ 飞书授权code为空")
            if is_feishu_app:
                return redirect('/?error=feishu_auth&message=授权码为空')
            else:
                return f"""
                <script>
                    window.opener.postMessage({{
                        type: 'feishu_auth_error',
                        error: '授权码为空'
                    }}, '*');
                    window.close();
                </script>
                """
        
        # 用code换取access_token，传递相应的redirect_uri
        from feishu_config import FEISHU_CONFIG
        base_redirect_uri = FEISHU_CONFIG['redirect_uri']
        if is_feishu_app:
            redirect_uri = f"{base_redirect_uri}?from_feishu_app=true"
        else:
            redirect_uri = base_redirect_uri
        
        token_data = feishu_auth.exchange_code_for_token(code, redirect_uri)
        if not token_data:
            print("❌ 飞书token获取失败")
            if is_feishu_app:
                return redirect('/?error=feishu_auth&message=token获取失败')
            else:
                return f"""
                <script>
                    window.opener.postMessage({{
                        type: 'feishu_auth_error',
                        error: 'token获取失败'
                    }}, '*');
                    window.close();
                </script>
                """
        
        access_token = token_data.get('access_token')
        if not access_token:
            print("❌ 飞书access_token为空")
            if is_feishu_app:
                return redirect('/?error=feishu_auth&message=access_token为空')
            else:
                return f"""
                <script>
                    window.opener.postMessage({{
                        type: 'feishu_auth_error',
                        error: 'access_token为空'
                    }}, '*');
                    window.close();
                </script>
                """
        
        # 获取用户信息
        user_info = feishu_auth.get_user_info(access_token)
        if not user_info:
            print("❌ 飞书用户信息获取失败")
            if is_feishu_app:
                return redirect('/?error=feishu_auth&message=用户信息获取失败')
            else:
                return f"""
                <script>
                    window.opener.postMessage({{
                        type: 'feishu_auth_error',
                        error: '用户信息获取失败'
                    }}, '*');
                    window.close();
                </script>
                """
        
        # 认证用户（如果不存在则创建）
        user = feishu_auth.authenticate_user(user_info)
        if not user:
            print("❌ 飞书用户认证失败")
            if is_feishu_app:
                return redirect('/?error=feishu_auth&message=用户认证失败')
            else:
                return f"""
                <script>
                    window.opener.postMessage({{
                        type: 'feishu_auth_error',
                        error: '用户认证失败'
                    }}, '*');
                    window.close();
                </script>
                """
        
        # 设置session
        session['user_id'] = user['id']
        session['username'] = user['username']
        session['user_email'] = user.get('useryx', '')
        session['login_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        session['login_type'] = 'feishu'
        
        # 简化session处理
        session.permanent = True
        print(f"✅ Session设置完成: user_id={user['id']}, username={user['username']}")
        
        print(f"✅ 飞书用户登录成功: {user['username']} (ID: {user['id']})")
        
        # 根据环境返回不同的响应
        if is_feishu_app:
            # 飞书应用内环境，直接重定向到首页
            print("📱 飞书应用内环境，直接重定向到首页")
            return redirect('/?success=login&message=飞书登录成功')
        else:
            # 浏览器环境，使用postMessage通知父窗口
            print("🌐 浏览器环境，使用postMessage通知父窗口")
            return f"""
            <script>
                window.opener.postMessage({{
                    type: 'feishu_auth_success',
                    user: {{
                        id: {user['id']},
                        username: '{user['username']}',
                        email: '{user.get('useryx', '')}',
                        login_time: '{session['login_time']}'
                    }}
                }}, '*');
                window.close();
            </script>
            """
        
    except Exception as e:
        print(f"❌ 飞书回调处理异常: {e}")
        # 检测是否在飞书应用内（在异常处理中也需要检测）
        user_agent = request.headers.get('User-Agent', '').lower()
        is_feishu_app = ('lark' in user_agent or 'feishu' in user_agent or 
                        'bytedance' in user_agent or 
                        request.args.get('from_feishu_app') == 'true')
        
        if is_feishu_app:
            return redirect(f'/?error=feishu_auth&message=系统错误: {str(e)}')
        else:
            return f"""
            <script>
                window.opener.postMessage({{
                    type: 'feishu_auth_error',
                    error: '系统错误: {str(e)}'
                }}, '*');
                window.close();
            </script>
            """

@app.route('/api/logout', methods=['POST'])
def logout():
    """用户登出"""
    try:
        username = session.get('username', 'Unknown')
        session.clear()
        print(f"✅ 用户登出: {username}")
        return jsonify({'success': True, 'message': '登出成功'})
    except Exception as e:
        print(f"❌ 登出处理异常: {e}")
        return jsonify({'success': False, 'message': f'登出处理失败: {str(e)}'}), 500

@app.route('/api/user/status', methods=['GET'])
def user_status():
    """检查用户登录状态"""
    try:
        print(f"🔍 检查登录状态 - Session keys: {list(session.keys())}")
        print(f"🔍 Session内容: {dict(session)}")
        
        if 'user_id' in session:
            user_data = {
                'success': True,
                'logged_in': True,
                'user': {
                    'id': session['user_id'],
                    'username': session['username'],
                    'login_time': session.get('login_time')
                }
            }
            print(f"✅ 用户已登录 - 返回数据: {user_data}")
            return jsonify(user_data)
        else:
            response_data = {
                'success': True,
                'logged_in': False,
                'message': '未登录'
            }
            print(f"❌ 用户未登录 - 返回数据: {response_data}")
            return jsonify(response_data)
    except Exception as e:
        print(f"❌ 检查登录状态异常: {e}")
        return jsonify({'success': False, 'message': f'状态检查失败: {str(e)}'}), 500

@app.route('/api/customer/query', methods=['GET'])
@login_required
def query_customer():
    """查询客户手机号 - 需要登录"""
    keyword = None
    try:
        keyword = request.args.get('keyword')
        if not keyword:
            return jsonify({'success': False, 'message': '缺少keyword参数'}), 400

        # 获取查询类型参数，默认为普通查询
        is_batch = request.args.get('batch', 'false').lower() == 'true'
        query_type = '批量查询' if is_batch else '普通查询'

        # 获取用户信息和客户端信息
        user_info = get_user_info_from_session()
        ip_address, user_agent = get_client_info()

        print(f"📋 用户 {user_info['user_name']} {query_type}: {keyword}")

        token = get_cached_token()
        if not token:
            # 记录失败日志
            query_logger.log_query(
                user_id=user_info['user_id'],
                user_email=user_info['user_email'],
                user_name=user_info['user_name'],
                member_code=keyword,
                query_success=False,
                query_type=query_type,
                error_message='Token获取失败',
                ip_address=ip_address,
                user_agent=user_agent
            )
            return jsonify({'success': False, 'message': 'Token获取失败'}), 500

        result = query_customer_info(keyword, token)
        if result:
            print(f"✅ 用户 {user_info['user_name']} {query_type}成功: {keyword}")

            # 记录成功日志
            query_logger.log_query(
                user_id=user_info['user_id'],
                user_email=user_info['user_email'],
                user_name=user_info['user_name'],
                member_code=keyword,
                query_success=True,
                query_type=query_type,
                response_data=result,
                ip_address=ip_address,
                user_agent=user_agent
            )

            return jsonify({'success': True, 'data': result, 'message': '查询成功'})
        else:
            print(f"❌ 用户 {user_info['user_name']} {query_type}失败: {keyword}")

            # 记录失败日志
            query_logger.log_query(
                user_id=user_info['user_id'],
                user_email=user_info['user_email'],
                user_name=user_info['user_name'],
                member_code=keyword,
                query_success=False,
                query_type=query_type,
                error_message='查询不到该会员号的相关消息',
                ip_address=ip_address,
                user_agent=user_agent
            )

            return jsonify({'success': False, 'message': '查询不到该会员号的相关消息'}), 200

    except Exception as e:
        user_info = get_user_info_from_session()
        ip_address, user_agent = get_client_info()

        print(f"❌ 用户 {user_info['user_name']} 查询异常: {keyword} - {str(e)}")

        # 获取查询类型
        is_batch = request.args.get('batch', 'false').lower() == 'true'
        query_type = '批量查询' if is_batch else '普通查询'

        # 记录异常日志
        if keyword:
            query_logger.log_query(
                user_id=user_info['user_id'],
                user_email=user_info['user_email'],
                user_name=user_info['user_name'],
                member_code=keyword,
                query_success=False,
                query_type=query_type,
                error_message=f'服务器错误: {str(e)}',
                ip_address=ip_address,
                user_agent=user_agent
            )

        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/customer/batch-query', methods=['POST'])
@login_required
def batch_query_customer():
    """批量查询客户手机号 - 需要登录"""
    try:
        data = request.get_json()
        if not data or 'member_codes' not in data:
            return jsonify({'success': False, 'message': '缺少member_codes参数'}), 400

        member_codes = data.get('member_codes', [])
        if not member_codes:
            return jsonify({'success': False, 'message': '会员号列表不能为空'}), 400

        # 获取用户信息和客户端信息
        user_info = get_user_info_from_session()
        ip_address, user_agent = get_client_info()

        print(f"📋 用户 {user_info['user_name']} 开始批量查询: {len(member_codes)} 个会员号")

        token = get_cached_token()
        if not token:
            return jsonify({'success': False, 'message': 'Token获取失败'}), 500

        # 批量查询结果
        batch_results = []
        log_results = []

        for member_code in member_codes:
            try:
                result = query_customer_info(member_code, token)
                if result:
                    batch_results.append({
                        'member_code': member_code,
                        'success': True,
                        'data': result
                    })
                    log_results.append({
                        'member_code': member_code,
                        'success': True,
                        'data': result,
                        'error': None
                    })
                else:
                    batch_results.append({
                        'member_code': member_code,
                        'success': False,
                        'error': '查询不到该会员号的相关消息'
                    })
                    log_results.append({
                        'member_code': member_code,
                        'success': False,
                        'data': None,
                        'error': '查询不到该会员号的相关消息'
                    })
            except Exception as e:
                error_msg = f'查询异常: {str(e)}'
                batch_results.append({
                    'member_code': member_code,
                    'success': False,
                    'error': error_msg
                })
                log_results.append({
                    'member_code': member_code,
                    'success': False,
                    'data': None,
                    'error': error_msg
                })

        # 记录批量查询日志
        query_logger.log_batch_query(
            user_id=user_info['user_id'],
            user_email=user_info['user_email'],
            user_name=user_info['user_name'],
            member_codes_results=log_results,
            ip_address=ip_address,
            user_agent=user_agent
        )

        successful_queries = len([r for r in batch_results if r['success']])
        failed_queries = len(batch_results) - successful_queries

        print(f"✅ 用户 {user_info['user_name']} 批量查询完成: 成功 {successful_queries} 条，失败 {failed_queries} 条")

        return jsonify({
            'success': True,
            'data': batch_results,
            'summary': {
                'total': len(batch_results),
                'successful': successful_queries,
                'failed': failed_queries
            },
            'message': f'批量查询完成: 成功 {successful_queries} 条，失败 {failed_queries} 条'
        })

    except Exception as e:
        user_info = get_user_info_from_session()
        print(f"❌ 用户 {user_info['user_name']} 批量查询异常: {str(e)}")
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/token/refresh', methods=['POST'])
def refresh_token_endpoint():
    """手动刷新Token"""
    try:
        token = refresh_token()
        if token:
            current_time = time.time() * 1000
            valid_duration = (token_cache['expire_time'] - current_time) / 1000 / 60
            return jsonify({
                'success': True,
                'message': 'Token刷新成功',
                'token': token[:20] + '...',
                'expire_time': token_cache['expire_time'],
                'valid_duration_minutes': round(valid_duration, 1)
            })
        else:
            return jsonify({'success': False, 'message': 'Token刷新失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/token/status', methods=['GET'])
def token_status():
    """查看Token状态"""
    try:
        current_time = time.time() * 1000
        if token_cache['token']:
            valid_duration = (token_cache['expire_time'] - current_time) / 1000 / 60
            is_valid = token_cache['expire_time'] > current_time + token_cache['buffer_seconds'] * 1000
            return jsonify({
                'success': True,
                'has_token': True,
                'token': token_cache['token'][:20] + '...' if token_cache['token'] else None,
                'expire_time': token_cache['expire_time'],
                'current_time': current_time,
                'valid_duration_minutes': round(valid_duration, 1),
                'is_valid': is_valid,
                'buffer_seconds': token_cache['buffer_seconds']
            })
        else:
            return jsonify({
                'success': True,
                'has_token': False,
                'message': '暂无缓存Token'
            })
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/query-logs', methods=['GET'])
@login_required
def get_query_logs():
    """获取查询日志 - 需要登录"""
    try:
        # 获取查询参数
        user_id = request.args.get('user_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        query_type = request.args.get('query_type')
        limit = request.args.get('limit', 100, type=int)

        # 如果没有指定user_id，默认查询当前用户的日志
        if not user_id:
            user_id = session.get('user_id')

        # 获取日志
        logs = query_logger.get_query_logs(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
            query_type=query_type,
            limit=limit
        )

        # 获取统计信息
        stats = query_logger.get_query_statistics(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date
        )

        return jsonify({
            'success': True,
            'data': {
                'logs': logs,
                'statistics': stats
            },
            'message': f'获取到 {len(logs)} 条日志记录'
        })

    except Exception as e:
        print(f"❌ 获取查询日志失败: {e}")
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

# 其他路由


if __name__ == '__main__':
    print("🚀 智美客户手机号查询系统")
    print("=" * 50)
    print("📍 访问地址: http://localhost:5000")
    print("🔐 需要登录验证")
    print("")
    print("📡 API接口:")
    print("  - GET  /api/feishu/auth   - 飞书OAuth授权")
    print("  - GET  /api/feishu/callback - 飞书OAuth回调")
    print("  - POST /api/logout        - 用户登出")
    print("  - GET  /api/user/status   - 检查登录状态")
    print("  - GET  /api/customer/query - 查询客户信息 (需登录)")
    print("  - GET  /api/token/status  - 查看Token状态")
    print("  - POST /api/token/refresh - 手动刷新Token")
    print("")

    # 测试数据库连接
    print("🔍 测试数据库连接...")
    if test_database_connection():
        print("✅ 数据库连接正常")
    else:
        print("❌ 数据库连接失败，请检查配置")

    print("=" * 50)
    print("🎯 服务器启动中...")

    app.run(host='0.0.0.0', port=5010, debug=False)
