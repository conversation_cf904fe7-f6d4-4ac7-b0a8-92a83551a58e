#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化版本的系统启动脚本
"""

import os
import sys
import time
from database import test_database_connection

def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    
    # 检查必要的模块
    required_modules = ['flask', 'flask_cors', 'requests', 'pymysql']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少必要模块: {', '.join(missing_modules)}")
        print("请运行: pip install flask flask-cors requests pymysql")
        return False
    
    print("✅ 系统要求检查通过")
    return True

def check_files():
    """检查必要文件"""
    print("🔍 检查必要文件...")
    
    required_files = [
        'web_server.py',
        'database.py',
        'query_logger.py',
        'index.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 文件检查通过")
    return True

def optimize_environment():
    """优化环境设置"""
    print("🔧 优化环境设置...")

    # 设置环境变量
    os.environ['FLASK_ENV'] = 'production'
    os.environ['PYTHONUNBUFFERED'] = '1'

    # 设置本地域名
    if 'BASE_DOMAIN' not in os.environ:
        os.environ['BASE_DOMAIN'] = 'http://localhost:5000'

    print("✅ 环境优化完成")

def start_server():
    """启动服务器"""
    print("🚀 启动服务器...")
    
    try:
        # 导入并启动web服务器
        from web_server import app
        
        print("=" * 60)
        print("🎯 智美客户手机号查询系统")
        print("=" * 60)
        print(f"📍 访问地址: http://localhost:5000")
        print(f"🔐 登录方式: 用户名密码登录")
        print("=" * 60)
        print("🎯 服务器启动中...")
        
        # 启动Flask应用
        app.run(
            host='0.0.0.0', 
            port=5000, 
            debug=False,  # 生产环境关闭调试模式
            threaded=True,  # 启用多线程
            use_reloader=False  # 关闭自动重载
        )
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 智美客户手机号查询系统 - 优化启动")
    print("=" * 60)
    
    # 1. 检查系统要求
    if not check_requirements():
        print("❌ 系统要求检查失败，请解决后重试")
        return False
    
    # 2. 检查必要文件
    if not check_files():
        print("❌ 文件检查失败，请确保所有必要文件存在")
        return False
    
    # 3. 测试数据库连接
    print("🔍 测试数据库连接...")
    if not test_database_connection():
        print("❌ 数据库连接失败，请检查配置")
        return False
    
    # 4. 优化环境
    optimize_environment()
    
    # 5. 启动服务器
    return start_server()

if __name__ == "__main__":
    success = main()
    if not success:
        print("❌ 系统启动失败")
        sys.exit(1)
