<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查询日志管理 - 智美客户手机号查询系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .stat-card h3 {
            font-size: 14px;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
        }

        .filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .filter-group input,
        .filter-group select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .logs-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .status-success {
            color: #28a745;
            font-weight: bold;
        }

        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }

        .query-type-normal {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .query-type-batch {
            background: #f3e5f5;
            color: #7b1fa2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 查询日志管理</h1>
            <p>智美客户手机号查询系统 - 日志监控面板</p>
        </div>

        <div id="error-message" class="error" style="display: none;"></div>

        <div class="stats-grid" id="stats-grid">
            <div class="stat-card">
                <h3>总查询次数</h3>
                <div class="value" id="total-queries">-</div>
            </div>
            <div class="stat-card">
                <h3>成功次数</h3>
                <div class="value" id="successful-queries">-</div>
            </div>
            <div class="stat-card">
                <h3>失败次数</h3>
                <div class="value" id="failed-queries">-</div>
            </div>
            <div class="stat-card">
                <h3>成功率</h3>
                <div class="value" id="success-rate">-</div>
            </div>
            <div class="stat-card">
                <h3>普通查询</h3>
                <div class="value" id="normal-queries">-</div>
            </div>
            <div class="stat-card">
                <h3>批量查询</h3>
                <div class="value" id="batch-queries">-</div>
            </div>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label>开始日期</label>
                <input type="date" id="start-date">
            </div>
            <div class="filter-group">
                <label>结束日期</label>
                <input type="date" id="end-date">
            </div>
            <div class="filter-group">
                <label>查询类型</label>
                <select id="query-type">
                    <option value="">全部</option>
                    <option value="普通查询">普通查询</option>
                    <option value="批量查询">批量查询</option>
                </select>
            </div>
            <div class="filter-group">
                <label>显示条数</label>
                <select id="limit">
                    <option value="50">50条</option>
                    <option value="100" selected>100条</option>
                    <option value="200">200条</option>
                    <option value="500">500条</option>
                </select>
            </div>
            <div class="filter-group">
                <button class="btn btn-primary" onclick="loadLogs()">🔍 查询</button>
            </div>
            <div class="filter-group">
                <button class="btn btn-primary" onclick="refreshLogs()">🔄 刷新</button>
            </div>
        </div>

        <div class="logs-table">
            <div id="loading" class="loading">正在加载日志数据...</div>
            <table id="logs-table" style="display: none;">
                <thead>
                    <tr>
                        <th>查询时间</th>
                        <th>用户</th>
                        <th>邮箱</th>
                        <th>会员号</th>
                        <th>状态</th>
                        <th>类型</th>
                        <th>IP地址</th>
                        <th>错误信息</th>
                    </tr>
                </thead>
                <tbody id="logs-tbody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 页面加载时自动加载日志
        document.addEventListener('DOMContentLoaded', function() {
            loadLogs();
        });

        // 加载日志数据
        async function loadLogs() {
            try {
                showLoading(true);
                hideError();

                // 构建查询参数
                const params = new URLSearchParams();
                
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                const queryType = document.getElementById('query-type').value;
                const limit = document.getElementById('limit').value;

                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                if (queryType) params.append('query_type', queryType);
                if (limit) params.append('limit', limit);

                const response = await fetch(`/api/query-logs?${params}`);
                const result = await response.json();

                if (result.success) {
                    updateStats(result.data.statistics);
                    updateLogsTable(result.data.logs);
                } else {
                    showError('加载日志失败: ' + result.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 刷新日志
        function refreshLogs() {
            loadLogs();
        }

        // 更新统计信息
        function updateStats(stats) {
            document.getElementById('total-queries').textContent = stats.total_queries || 0;
            document.getElementById('successful-queries').textContent = stats.successful_queries || 0;
            document.getElementById('failed-queries').textContent = stats.failed_queries || 0;
            document.getElementById('success-rate').textContent = (parseFloat(stats.success_rate) || 0).toFixed(2) + '%';
            document.getElementById('normal-queries').textContent = stats.normal_queries || 0;
            document.getElementById('batch-queries').textContent = stats.batch_queries || 0;
        }

        // 更新日志表格
        function updateLogsTable(logs) {
            const tbody = document.getElementById('logs-tbody');
            tbody.innerHTML = '';

            if (logs.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666;">暂无日志数据</td></tr>';
                return;
            }

            logs.forEach(log => {
                const row = document.createElement('tr');
                
                const queryTime = new Date(log.query_time).toLocaleString('zh-CN');
                const status = log.query_success ? '成功' : '失败';
                const statusClass = log.query_success ? 'status-success' : 'status-failed';
                const typeClass = log.query_type === '普通查询' ? 'query-type-normal' : 'query-type-batch';
                
                row.innerHTML = `
                    <td>${queryTime}</td>
                    <td>${log.user_name || '-'}</td>
                    <td>${log.user_email || '-'}</td>
                    <td>${log.member_code}</td>
                    <td><span class="${statusClass}">${status}</span></td>
                    <td><span class="${typeClass}">${log.query_type}</span></td>
                    <td>${log.ip_address || '-'}</td>
                    <td>${log.error_message || '-'}</td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('logs-table').style.display = show ? 'none' : 'table';
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 隐藏错误信息
        function hideError() {
            document.getElementById('error-message').style.display = 'none';
        }

        // 设置默认日期（最近7天）
        function setDefaultDates() {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 7);

            document.getElementById('end-date').value = endDate.toISOString().split('T')[0];
            document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
        }

        // 页面加载时设置默认日期
        // setDefaultDates();
    </script>
</body>
</html>
