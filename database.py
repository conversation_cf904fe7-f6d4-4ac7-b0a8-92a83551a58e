#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库连接和用户认证模块
"""

import pymysql
import json
from datetime import datetime, timedelta
import logging

# 数据库配置
DB_CONFIG = {
    'host': 'rm-bp135o9085r8yeu7tlo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'zmuser',
    'password': 'Zm&83247778',
    'database': 'ceff',
    'charset': 'utf8mb4',
    'autocommit': True,
    'init_command': "SET time_zone = '+8:00'",  # 设置为中国时区 UTC+8
}

# 测试数据库连接
def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        
        # 测试查询用户表结构
        cursor = conn.cursor()
        cursor.execute("DESCRIBE cxljsj_user")
        columns = cursor.fetchall()
        
        print("✅ 数据库连接测试成功")
        print("✅ 用户表结构:")
        for column in columns:
            print(f"  - {column[0]}: {column[1]}")
        
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

class DatabaseManager:
    """数据库管理类 - 提供数据库连接和操作功能"""
    
    def __init__(self):
        self.connection = None
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**DB_CONFIG)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query, params=None):
        """执行查询SQL"""
        if not self.connection and not self.connect():
            return None
        
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(query, params)
                result = cursor.fetchall()
                return result
        except Exception as e:
            print(f"❌ 执行查询失败: {e}")
            return None
    
    def execute_update(self, query, params=None):
        """执行更新SQL"""
        if not self.connection and not self.connect():
            return False
        
        try:
            with self.connection.cursor() as cursor:
                affected_rows = cursor.execute(query, params)
                self.connection.commit()
                return affected_rows
        except Exception as e:
            print(f"❌ 执行更新失败: {e}")
            self.connection.rollback()
            return False
    
    def find_user_by_username(self, username):
        """根据用户名查找用户"""
        query = "SELECT * FROM cxljsj_user WHERE username = %s LIMIT 1"
        result = self.execute_query(query, (username,))
        return result[0] if result else None

    def find_user_by_email_or_feishu_id(self, login_identifier):
        """根据邮箱或飞书用户ID查找用户"""
        query = """
        SELECT * FROM cxljsj_user
        WHERE useryx = %s OR feishu_user_id = %s
        LIMIT 1
        """
        result = self.execute_query(query, (login_identifier, login_identifier))
        return result[0] if result else None
    
    def find_user_by_feishu_id(self, feishu_user_id):
        """根据飞书用户ID查找用户"""
        query = "SELECT * FROM cxljsj_user WHERE feishu_user_id = %s LIMIT 1"
        result = self.execute_query(query, (feishu_user_id,))
        return result[0] if result else None
    
    # 其他数据库方法

class UserAuth:
    """用户认证类 - 提供用户认证相关功能"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def authenticate(self, login_identifier, password):
        """验证用户名/邮箱和密码"""
        if not self.db.connect():
            return None

        try:
            # 首先尝试通过用户名查找用户
            user = self.db.find_user_by_username(login_identifier)
            
            # 如果用户名没找到，再尝试通过邮箱查找
            if not user:
                user = self.db.find_user_by_email_or_feishu_id(login_identifier)
            
            if not user:
                return None

            # 直接验证密码（不使用哈希）
            if user.get('password') != password:
                return None

            return user
        finally:
            self.db.disconnect()


# 用户认证类
